<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <!-- 按钮组区 -->
            <a-button type="primary" @click="handleExport">导出当前表格</a-button>
            <a-button @click="handleExport">导出答题明细</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <!-- 表格列插槽区 -->
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === '正常' ? 'success' : 'error'">
                {{ record.status }}
              </a-tag>
            </template>
            <template v-if="column.key === 'enrollmentStatus'">
              {{ record.enrollmentStatus === '未参加' ? '未参加' : '已参加' }}
            </template>
            <template v-if="column.key === 'dateOfBirth'">
              {{ record.dateOfBirth | formatDate }}
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 导出 -->
    <ExportModal @register="registerExportModal" />
  </div>
</template>

<script lang="ts" setup>
  import { useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import { onMounted, ref } from 'vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { useBaseStore } from '@/store/modules/base';

  const props = defineProps(['wjdm']);
  const api = useBaseApi('/api/knsDcwjPerson');

  // #region table
  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '学号',
      dataIndex: 'studentId',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      width: 90,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '出生日期',
      dataIndex: 'dateOfBirth',
      width: 150,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '院系',
      dataIndex: 'department',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'major',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'class',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '现在年级',
      dataIndex: 'currentGrade',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学籍状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '参加情况',
      dataIndex: 'enrollmentStatus',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '填写学年',
      dataIndex: 'txxn',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '填写学期',
      dataIndex: 'txxq',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
  ];

  // 注册表格
  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    columns,
  });

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: () => {},
      },
    ];
  }

  // #endregion

  // #region 导出
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  function handleExport() {
    openExportModal(true, { columnList: columns });
  }
  // #endregion
</script>
